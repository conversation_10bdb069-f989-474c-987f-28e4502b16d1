import 'package:flutter/material.dart';
import 'package:clockin/services/reports_service.dart';
import 'package:clockin/constants/app_constants.dart';

/// Widget de débogage pour tester les rapports avec le backend existant
/// À ajouter temporairement dans l'application pour valider la solution
class DebugReportsWidget extends StatefulWidget {
  const DebugReportsWidget({super.key});

  @override
  State<DebugReportsWidget> createState() => _DebugReportsWidgetState();
}

class _DebugReportsWidgetState extends State<DebugReportsWidget> {
  final ReportsService _reportsService = ReportsService();
  String _testResult = 'Appuyez sur un bouton pour tester';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Rapports'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Configuration actuelle
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Configuration Actuelle',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('URL de base: ${AppConstants.baseUrl}'),
                    Text('Endpoint rapports: ${AppConstants.reportsEndpoint}'),
                    Text('Endpoint pointage: ${AppConstants.pointageEndpoint}'),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        'Cette version utilise un fallback vers /api/pointage si /api/reports/attendance n\'est pas disponible',
                        style: TextStyle(
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Boutons de test
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testGenerateReport(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Test Génération Rapport'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testFetchAttendance(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Test Récupération Données'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testFullWorkflow(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Test Complet'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Résultats
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Résultats des Tests',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _testResult,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testGenerateReport() async {
    setState(() {
      _isLoading = true;
      _testResult = '🧪 Test de génération de rapport...\n';
    });

    try {
      final startDate = DateTime.now().subtract(const Duration(days: 7));
      final endDate = DateTime.now();

      _appendResult(
        '📅 Période: ${startDate.toIso8601String().split('T')[0]} à ${endDate.toIso8601String().split('T')[0]}',
      );
      _appendResult('🔄 Génération du rapport...');

      final report = await _reportsService.generateEmployeeReport(
        startDate: startDate.toIso8601String().split('T')[0],
        endDate: endDate.toIso8601String().split('T')[0],
        includeStats: true,
      );

      _appendResult('✅ Rapport généré avec succès !');
      _appendResult('📄 Nom du fichier: ${report.filename}');
      _appendResult('🔗 URL de téléchargement: ${report.downloadUrl}');
      _appendResult('📏 Taille: ${report.fileSize}');
      _appendResult('⏰ Généré à: ${report.generatedAt}');
    } catch (e) {
      _appendResult('❌ Erreur lors de la génération: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testFetchAttendance() async {
    setState(() {
      _isLoading = true;
      _testResult = '🧪 Test de récupération des données de présence...\n';
    });

    try {
      final startDate = DateTime.now().subtract(const Duration(days: 7));
      final endDate = DateTime.now();

      _appendResult(
        '📅 Période: ${startDate.toIso8601String().split('T')[0]} à ${endDate.toIso8601String().split('T')[0]}',
      );
      _appendResult('🔄 Récupération des données...');

      final result = await _reportsService.generateAndDownloadCSVReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );

      if (result != null && result.success) {
        _appendResult('✅ Données récupérées avec succès !');
        _appendResult('📄 Nom du fichier: ${result.filename}');
        _appendResult('💾 Chemin: ${result.filePath ?? 'N/A'}');
        _appendResult(
          '📊 Contenu (extrait): ${result.content.length > 100 ? result.content.substring(0, 100) + '...' : result.content}',
        );
      } else {
        _appendResult('❌ Échec de la récupération des données');
        _appendResult('📄 Message: ${result?.content ?? 'Résultat null'}');
      }
    } catch (e) {
      _appendResult('❌ Erreur lors de la récupération: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testFullWorkflow() async {
    setState(() {
      _isLoading = true;
      _testResult = '🧪 Test du workflow complet...\n';
    });

    try {
      final startDate = DateTime.now().subtract(const Duration(days: 7));
      final endDate = DateTime.now();

      _appendResult(
        '📅 Période: ${startDate.toIso8601String().split('T')[0]} à ${endDate.toIso8601String().split('T')[0]}',
      );

      // Test 1: Génération de rapport via API
      _appendResult('\n🔄 Étape 1: Génération de rapport via API...');
      try {
        final report = await _reportsService.generateEmployeeReport(
          startDate: startDate.toIso8601String().split('T')[0],
          endDate: endDate.toIso8601String().split('T')[0],
          includeStats: true,
        );
        _appendResult('✅ Étape 1 réussie - Rapport: ${report.filename}');
      } catch (e) {
        _appendResult('❌ Étape 1 échouée: $e');
      }

      // Test 2: Récupération et génération CSV
      _appendResult('\n🔄 Étape 2: Récupération données et génération CSV...');
      final csvResult = await _reportsService.generateAndDownloadCSVReport(
        reportType: 'all_employees',
        startDate: startDate,
        endDate: endDate,
      );

      if (csvResult != null && csvResult.success) {
        _appendResult('✅ Étape 2 réussie - CSV: ${csvResult.filename}');
        _appendResult('📊 Données trouvées et fichier généré');
      } else {
        _appendResult(
          '❌ Étape 2 échouée: ${csvResult?.content ?? 'Résultat null'}',
        );
      }

      _appendResult('\n🎉 Test complet terminé !');
    } catch (e) {
      _appendResult('❌ Erreur dans le workflow: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _appendResult(String message) {
    setState(() {
      _testResult += '$message\n';
    });
  }
}

/// Extension pour ajouter facilement le widget de debug à l'application
/// Ajoutez ceci dans votre main.dart ou dans un écran admin :
///
/// FloatingActionButton(
///   onPressed: () {
///     Navigator.push(
///       context,
///       MaterialPageRoute(builder: (context) => const DebugReportsWidget()),
///     );
///   },
///   child: const Icon(Icons.bug_report),
/// )
