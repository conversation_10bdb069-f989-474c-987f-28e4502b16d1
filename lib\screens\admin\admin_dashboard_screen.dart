import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../routes/app_routes.dart';
import '../../utils/responsive_utils.dart';
import '../../widgets/common/modern_card.dart';
import '../../widgets/common/modern_drawer.dart';
import '../../widgets/common/responsive_stat_card.dart';
import '../../widgets/common/responsive_grid.dart';
import '../../widgets/dashboard/modern_welcome_card.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        context.read<EmployeesProvider>().loadEmployeesData();
        context.read<SitesProvider>().loadSitesData();
        context.read<AttendanceProvider>().loadAttendanceData();
      } catch (e) {
        debugPrint('Error loading dashboard data: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم الإدارة'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () =>
                Navigator.pushNamed(context, AppRoutes.settingsScreen),
          ),
        ],
      ),
      drawer: const ModernDrawer(isAdmin: true),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Navigator.pushNamed(context, AppRoutes.addEmployee),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        icon: const Icon(Icons.person_add),
        label: const Text('إضافة موظف'),
      ),
      body: RefreshIndicator(
        onRefresh: () async => _loadData(),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final maxWidth = ResponsiveUtils.getMaxContentWidth(context);
            return Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(maxWidth: maxWidth),
                child: SingleChildScrollView(
                  padding: ResponsiveUtils.getResponsivePadding(context),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildModernWelcomeCard(),
                      SizedBox(
                        height: ResponsiveUtils.getResponsiveSpacing(
                          context,
                          20,
                        ),
                      ),
                      _buildResponsiveStatsCards(),
                      SizedBox(
                        height: ResponsiveUtils.getResponsiveSpacing(
                          context,
                          20,
                        ),
                      ),
                      _buildModernQuickActions(),
                      SizedBox(
                        height: ResponsiveUtils.getResponsiveSpacing(
                          context,
                          20,
                        ),
                      ),
                      _buildChartsSection(),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildModernWelcomeCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return ModernWelcomeCard(
          user: authProvider.user,
          customGreeting: 'مرحباً بك في لوحة التحكم',
          showTime: true,
          gradientColors: const [
            AppColors.primaryBlue,
            AppColors.primaryBlueDark,
          ],
          action: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.textWhite.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.textWhite.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.admin_panel_settings,
                  color: AppColors.textWhite.withValues(alpha: 0.9),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'لوحة الإدارة',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textWhite.withValues(alpha: 0.9),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildResponsiveStatsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveSectionHeader(
          title: 'إحصائيات سريعة',
          subtitle: 'نظرة عامة على النظام',
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Consumer3<EmployeesProvider, SitesProvider, AttendanceProvider>(
          builder:
              (
                context,
                employeesProvider,
                sitesProvider,
                attendanceProvider,
                child,
              ) {
                final statCards = [
                  ResponsiveStatCard(
                    title: 'إجمالي الموظفين',
                    value: '${employeesProvider.employees.length}',
                    icon: Icons.people,
                    color: AppColors.primaryBlue,
                    subtitle: 'موظف مسجل',
                    showTrend: true,
                    trendValue: '+5%',
                    trendIsPositive: true,
                    onTap: () => Navigator.pushNamed(
                      context,
                      AppRoutes.employeesManagement,
                    ),
                  ),
                  ResponsiveStatCard(
                    title: 'المواقع النشطة',
                    value: '${sitesProvider.sites.length}',
                    icon: Icons.location_on,
                    color: AppColors.success,
                    subtitle: 'موقع عمل',
                    showTrend: true,
                    trendValue: '+2',
                    trendIsPositive: true,
                    onTap: () =>
                        Navigator.pushNamed(context, AppRoutes.sitesManagement),
                  ),
                  ResponsiveStatCard(
                    title: 'الحضور اليوم',
                    value: '${attendanceProvider.todayAttendanceCount}',
                    icon: Icons.access_time,
                    color: AppColors.warning,
                    subtitle: 'حضور مسجل',
                    showTrend: true,
                    trendValue: '85%',
                    trendIsPositive: true,
                    onTap: () => Navigator.pushNamed(
                      context,
                      AppRoutes.pointagesManagement,
                    ),
                  ),
                  ResponsiveStatCard(
                    title: 'التقارير',
                    value: '12',
                    icon: Icons.assessment,
                    color: AppColors.info,
                    subtitle: 'تقرير متاح',
                    showTrend: false,
                    onTap: () =>
                        Navigator.pushNamed(context, AppRoutes.reportsScreen),
                  ),
                ];

                return ResponsiveGrid(
                  children: statCards,
                  maxColumns: 4,
                  childAspectRatio: ResponsiveUtils.getCardAspectRatio(context),
                  spacing: ResponsiveUtils.getResponsiveSpacing(
                    context,
                    AppConstants.gridSpacing,
                  ),
                );
              },
        ),
      ],
    );
  }

  Widget _buildCurrentStatusCard({
    required String title,
    required String value,
    required String subtitle,
    required String subtitleValue,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          color: AppColors.surfaceWhite,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(color: AppColors.borderLight),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadow.withValues(alpha: 0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const Spacer(),
                Icon(icon, color: color, size: 20),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              subtitle,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 4),
            Text(
              subtitleValue,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveSectionHeader(
          title: 'إجراءات سريعة',
          subtitle: 'الوصول السريع للوظائف الأساسية',
        ),
        SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context, 16)),
        ResponsiveWrap(
          children: [
            _buildModernActionCard(
              title: 'إدارة الحضور',
              subtitle: 'عرض وإدارة سجلات الحضور',
              icon: Icons.access_time,
              color: AppColors.success,
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.pointagesManagement),
            ),
            _buildModernActionCard(
              title: 'مراقبة الحضور',
              subtitle: 'مراقبة الحضور في الوقت الفعلي',
              icon: Icons.monitor,
              color: AppColors.primaryBlue,
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.monitoringScreen),
            ),
            _buildModernActionCard(
              title: 'الإعدادات',
              subtitle: 'إعدادات النظام والتطبيق',
              icon: Icons.settings,
              color: AppColors.info,
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.settingsScreen),
            ),
            _buildModernActionCard(
              title: 'إدارة المواقع',
              subtitle: 'إدارة مواقع العمل والفروع',
              icon: Icons.location_on,
              color: AppColors.warning,
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.sitesManagement),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: color.withValues(alpha: 0.3)),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final cardWidth = deviceType == DeviceType.mobile
        ? double.infinity
        : (MediaQuery.of(context).size.width - 64) / 2;

    return Container(
      width: cardWidth,
      margin: EdgeInsets.only(
        bottom: ResponsiveUtils.getResponsiveSpacing(context, 12),
        right: deviceType != DeviceType.mobile ? 12 : 0,
      ),
      child: ResponsiveCard(
        onTap: onTap,
        padding: ResponsiveUtils.getResponsivePadding(context),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: color.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: color,
                size: ResponsiveUtils.getResponsiveIconSize(context, 24),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                        fontSize: ResponsiveUtils.getResponsiveFontSize(
                          context,
                          16,
                        ),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                      fontSize: ResponsiveUtils.getResponsiveFontSize(
                        context,
                        12,
                      ),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص الحضور',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.surfaceWhite,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: AppColors.borderLight),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildAttendanceChart(),
        ),
      ],
    );
  }

  Widget _buildAttendanceChart() {
    return Column(
      children: [
        Container(
          height: 120,
          decoration: BoxDecoration(
            color: AppColors.backgroundGrey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(color: AppColors.borderLight),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.bar_chart, size: 48, color: AppColors.textSecondary),
                SizedBox(height: 8),
                Text(
                  'لا توجد إحصائيات متاحة',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationDrawer() {
    return Drawer(
      child: Column(
        children: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              return UserAccountsDrawerHeader(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primaryBlue, AppColors.primaryBlueLight],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                accountName: Text(
                  authProvider.user?.name ?? 'المدير',
                  style: const TextStyle(
                    color: AppColors.textWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                accountEmail: Text(
                  authProvider.user?.email ?? '',
                  style: const TextStyle(color: AppColors.textWhite),
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundColor: AppColors.textWhite.withValues(alpha: 0.2),
                  child: Text(
                    authProvider.userInitials,
                    style: const TextStyle(
                      color: AppColors.textWhite,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            },
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.dashboard,
                  title: 'لوحة التحكم',
                  onTap: () => Navigator.pop(context),
                ),
                const Divider(),
                _buildDrawerItem(
                  icon: Icons.people,
                  title: 'إدارة الموظفين',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.employeesManagement);
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.person_add,
                  title: 'إضافة موظف',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.addEmployee);
                  },
                ),
                const Divider(),
                _buildDrawerItem(
                  icon: Icons.location_on,
                  title: 'إدارة المواقع',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.sitesManagement);
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.add_location,
                  title: 'إضافة موقع',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.addSite);
                  },
                ),
                const Divider(),
                _buildDrawerItem(
                  icon: Icons.assessment,
                  title: 'التقارير',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.reportsScreen);
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.monitor,
                  title: 'المراقبة المباشرة',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.monitoringScreen);
                  },
                ),
                const Divider(),

                _buildDrawerItem(
                  icon: Icons.settings,
                  title: 'الإعدادات',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.settingsScreen);
                  },
                ),
              ],
            ),
          ),
          const Divider(),
          _buildDrawerItem(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            onTap: () {
              Navigator.pop(context);
              _showLogoutDialog();
            },
            isDestructive: true,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppColors.error : AppColors.primaryBlue,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? AppColors.error : AppColors.textPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      dense: true,
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthProvider>().logout();
              Navigator.pushReplacementNamed(context, AppRoutes.login);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
