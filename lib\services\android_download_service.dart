import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';

/// Service spécialisé pour le téléchargement de fichiers Excel sur Android
/// Sauvegarde dans le dossier Téléchargements standard du système
class AndroidDownloadService {
  static const String _reportsFolder = 'ClockIn_Reports';

  /// Obtenir le chemin du dossier Téléchargements public d'Android
  Future<String?> getPublicDownloadsPath() async {
    try {
      // Chemin standard des téléchargements sur Android
      const downloadsPath = '/storage/emulated/0/Download';
      final downloadsDir = Directory(downloadsPath);

      if (await downloadsDir.exists()) {
        debugPrint(
          'AndroidDownloadService: Using public Downloads directory: $downloadsPath',
        );
        return downloadsPath;
      }

      // Fallback vers d'autres emplacements possibles
      const alternativePaths = [
        '/sdcard/Download',
        '/storage/sdcard0/Download',
        '/mnt/sdcard/Download',
      ];

      for (final path in alternativePaths) {
        final dir = Directory(path);
        if (await dir.exists()) {
          debugPrint(
            'AndroidDownloadService: Using alternative Downloads directory: $path',
          );
          return path;
        }
      }

      debugPrint('AndroidDownloadService: No Downloads directory found');
      return null;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error getting Downloads path: $e');
      return null;
    }
  }

  /// Demander toutes les permissions nécessaires pour Android
  Future<bool> requestAllPermissions() async {
    try {
      debugPrint(
        'AndroidDownloadService: Requesting all necessary permissions',
      );

      // Obtenir les informations sur la version Android
      final androidInfo = await _getAndroidSdkVersion();
      debugPrint('AndroidDownloadService: Android SDK version: $androidInfo');

      // Utiliser une approche simplifiée qui fonctionne sur toutes les versions Android
      debugPrint(
        'AndroidDownloadService: Requesting storage permissions (simplified approach)',
      );

      // Essayer d'abord les permissions de stockage classiques
      final storage = await Permission.storage.request();
      debugPrint('AndroidDownloadService: Storage permission: ${storage.name}');

      if (storage.isGranted) {
        return true;
      }

      // Si les permissions classiques échouent, essayer MANAGE_EXTERNAL_STORAGE
      if (androidInfo >= 30) {
        debugPrint(
          'AndroidDownloadService: Trying MANAGE_EXTERNAL_STORAGE for Android 11+',
        );
        final manageStorage = await Permission.manageExternalStorage.request();
        debugPrint(
          'AndroidDownloadService: MANAGE_EXTERNAL_STORAGE: ${manageStorage.name}',
        );

        if (manageStorage.isGranted) {
          return true;
        }
      }

      // Dernière tentative : permissions granulaires pour Android 13+
      if (androidInfo >= 33) {
        debugPrint(
          'AndroidDownloadService: Trying granular permissions for Android 13+',
        );
        final photos = await Permission.photos.request();
        debugPrint('AndroidDownloadService: Photos permission: ${photos.name}');
        return photos.isGranted;
      }

      // Si tout échoue, retourner false mais continuer quand même
      debugPrint(
        'AndroidDownloadService: All permission requests failed, but continuing anyway',
      );
      return false; // On essaiera quand même de sauvegarder
    } catch (e) {
      debugPrint('AndroidDownloadService: Error requesting permissions: $e');
      return false;
    }
  }

  /// Obtenir la version du SDK Android
  Future<int> _getAndroidSdkVersion() async {
    try {
      // Pour la compatibilité, supposons Android 11 (API 30)
      // Cela nous permet d'utiliser les permissions classiques qui fonctionnent mieux
      return 30;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error getting Android version: $e');
      return 30; // Fallback vers Android 11
    }
  }

  /// Sauvegarder un fichier CSV localement et l'ouvrir avec Excel
  Future<String?> saveAndOpenExcelFile(String filename, Uint8List bytes) async {
    try {
      debugPrint(
        'AndroidDownloadService: Saving CSV file locally for Excel app',
      );
      debugPrint('AndroidDownloadService: Filename: $filename');
      debugPrint('AndroidDownloadService: File size: ${bytes.length} bytes');

      // 1. Demander les permissions (mais continuer même si refusées)
      final hasPermissions = await requestAllPermissions();
      if (!hasPermissions) {
        debugPrint(
          'AndroidDownloadService: Permissions refusées, mais on continue quand même',
        );
      }

      // 2. Obtenir le chemin des téléchargements avec fallback
      String? downloadsPath = await getPublicDownloadsPath();

      if (downloadsPath == null) {
        debugPrint(
          'AndroidDownloadService: Public downloads not accessible, using app directory',
        );
        // Fallback vers le répertoire de l'application
        try {
          // Use path_provider to get the correct app directory
          final Directory appDir = await getApplicationDocumentsDirectory();
          if (!await appDir.exists()) {
            await appDir.create(recursive: true);
          }
          downloadsPath = appDir.path;
        } catch (e) {
          throw Exception(
            'Impossible d\'accéder à un dossier de sauvegarde: $e',
          );
        }
      }

      // 3. Créer le dossier ClockIn_Reports dans Downloads
      final reportsPath = '$downloadsPath/$_reportsFolder';
      final reportsDir = Directory(reportsPath);

      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
        debugPrint(
          'AndroidDownloadService: Created reports directory: $reportsPath',
        );
      }

      // 4. Sauvegarder le fichier Excel
      final filePath = '$reportsPath/$filename';
      final file = File(filePath);

      await file.writeAsBytes(bytes);

      // 5. Vérifier que le fichier a été créé
      if (await file.exists()) {
        final fileSize = await file.length();
        debugPrint('AndroidDownloadService: Excel file saved successfully');
        debugPrint('AndroidDownloadService: Path: $filePath');
        debugPrint('AndroidDownloadService: Size: $fileSize bytes');

        // 6. Notifier le système Android du nouveau fichier
        await _notifyMediaScanner(filePath);

        // 7. Ouvrir automatiquement avec Excel
        final opened = await openExcelFile(filePath);
        if (opened) {
          debugPrint('AndroidDownloadService: Excel file opened successfully');
        } else {
          debugPrint(
            'AndroidDownloadService: Could not open Excel file automatically',
          );
        }

        return filePath;
      } else {
        throw Exception('Le fichier Excel n\'a pas pu être créé');
      }
    } catch (e) {
      debugPrint('AndroidDownloadService: Error saving Excel file: $e');
      rethrow;
    }
  }

  /// Notifier le scanner de médias Android du nouveau fichier
  Future<void> _notifyMediaScanner(String filePath) async {
    try {
      // Sur Android, nous pouvons utiliser une méthode platform-specific
      // Pour l'instant, on simule cette notification
      debugPrint(
        'AndroidDownloadService: Notifying media scanner for: $filePath',
      );

      // Le fichier devrait maintenant être visible dans le gestionnaire de fichiers
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      debugPrint('AndroidDownloadService: Error notifying media scanner: $e');
    }
  }

  /// Ouvrir un fichier Excel avec l'application par défaut
  Future<bool> openExcelFile(String filePath) async {
    try {
      debugPrint('AndroidDownloadService: Opening Excel file: $filePath');

      final result = await OpenFile.open(filePath);

      if (result.type == ResultType.done) {
        debugPrint('AndroidDownloadService: File opened successfully');
        return true;
      } else {
        debugPrint(
          'AndroidDownloadService: Failed to open file: ${result.message}',
        );
        return false;
      }
    } catch (e) {
      debugPrint('AndroidDownloadService: Error opening Excel file: $e');
      return false;
    }
  }

  /// Partager un fichier Excel via WhatsApp, Email, etc.
  Future<bool> shareExcelFile(String filePath, String filename) async {
    try {
      debugPrint('AndroidDownloadService: Sharing Excel file: $filePath');

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Le fichier n\'existe pas: $filePath');
      }

      // Partage avec options spécifiques pour Excel
      await Share.shareXFiles(
        [XFile(filePath)],
        text:
            'Rapport ClockIn Excel: $filename\n\nFichier généré par l\'application ClockIn.',
        subject: 'Rapport ClockIn - $filename',
      );

      debugPrint('AndroidDownloadService: Excel file shared successfully');
      return true;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error sharing Excel file: $e');
      return false;
    }
  }

  /// Partager directement via WhatsApp (si installé)
  Future<bool> shareViaWhatsApp(String filePath, String filename) async {
    try {
      debugPrint(
        'AndroidDownloadService: Attempting to share via WhatsApp: $filePath',
      );

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Le fichier n\'existe pas: $filePath');
      }

      // Essayer de partager spécifiquement via WhatsApp
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'Rapport ClockIn Excel: $filename',
        subject: 'Rapport ClockIn',
      );

      debugPrint(
        'AndroidDownloadService: File shared via WhatsApp successfully',
      );
      return true;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error sharing via WhatsApp: $e');
      return false;
    }
  }

  /// Partager directement via Email
  Future<bool> shareViaEmail(String filePath, String filename) async {
    try {
      debugPrint(
        'AndroidDownloadService: Attempting to share via Email: $filePath',
      );

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Le fichier n\'existe pas: $filePath');
      }

      // Partager via Email avec sujet et corps prédéfinis
      await Share.shareXFiles(
        [XFile(filePath)],
        text:
            'Veuillez trouver ci-joint le rapport ClockIn au format Excel.\n\nNom du fichier: $filename\n\nCordialement,\nApplication ClockIn',
        subject: 'Rapport ClockIn - $filename',
      );

      debugPrint('AndroidDownloadService: File shared via Email successfully');
      return true;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error sharing via Email: $e');
      return false;
    }
  }

  /// Lister tous les fichiers Excel dans le dossier de rapports
  Future<List<FileInfo>> getDownloadedReports() async {
    try {
      debugPrint('AndroidDownloadService: Getting downloaded reports');

      final downloadsPath = await getPublicDownloadsPath();
      if (downloadsPath == null) {
        return [];
      }

      final reportsPath = '$downloadsPath/$_reportsFolder';
      final reportsDir = Directory(reportsPath);

      if (!await reportsDir.exists()) {
        debugPrint('AndroidDownloadService: Reports directory does not exist');
        return [];
      }

      final files = await reportsDir.list().toList();
      final reportFiles = <FileInfo>[];

      for (final file in files) {
        if (file is File && file.path.endsWith('.xlsx')) {
          final stat = await file.stat();
          reportFiles.add(
            FileInfo(
              name: file.path.split('/').last,
              path: file.path,
              size: stat.size,
              modifiedDate: stat.modified,
            ),
          );
        }
      }

      // Trier par date de modification (plus récent en premier)
      reportFiles.sort((a, b) => b.modifiedDate.compareTo(a.modifiedDate));

      debugPrint(
        'AndroidDownloadService: Found ${reportFiles.length} downloaded reports',
      );
      return reportFiles;
    } catch (e) {
      debugPrint(
        'AndroidDownloadService: Error getting downloaded reports: $e',
      );
      return [];
    }
  }

  /// Supprimer un fichier de rapport
  Future<bool> deleteReport(String filePath) async {
    try {
      debugPrint('AndroidDownloadService: Deleting report: $filePath');

      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        debugPrint('AndroidDownloadService: Report deleted successfully');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('AndroidDownloadService: Error deleting report: $e');
      return false;
    }
  }

  /// Formater la taille du fichier
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// Informations sur un fichier téléchargé
class FileInfo {
  final String name;
  final String path;
  final int size;
  final DateTime modifiedDate;

  FileInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.modifiedDate,
  });

  @override
  String toString() {
    return 'FileInfo(name: $name, size: $size, modified: $modifiedDate)';
  }
}
