import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';

enum CardVariant { elevated, outlined, filled }

class ModernCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final CardVariant variant;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? elevation;
  final BorderRadius? borderRadius;

  const ModernCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.variant = CardVariant.elevated,
    this.backgroundColor,
    this.borderColor,
    this.elevation,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      child: Material(
        color: _getBackgroundColor(theme),
        elevation: _getElevation(),
        shadowColor: _getShadowColor(),
        surfaceTintColor: _getSurfaceTintColor(),
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
          child: Container(
            decoration: _getDecoration(),
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor(ThemeData theme) {
    if (backgroundColor != null) return backgroundColor!;

    switch (variant) {
      case CardVariant.elevated:
        return AppColors.surfaceWhite;
      case CardVariant.outlined:
        return AppColors.surfaceWhite;
      case CardVariant.filled:
        return AppColors.surfaceGrey;
    }
  }

  double _getElevation() {
    if (elevation != null) return elevation!;

    switch (variant) {
      case CardVariant.elevated:
        return 2;
      case CardVariant.outlined:
        return 0;
      case CardVariant.filled:
        return 0;
    }
  }

  Color? _getShadowColor() {
    switch (variant) {
      case CardVariant.elevated:
        return AppColors.primaryBlue.withValues(alpha: 0.1);
      case CardVariant.outlined:
      case CardVariant.filled:
        return Colors.transparent;
    }
  }

  Color? _getSurfaceTintColor() {
    switch (variant) {
      case CardVariant.elevated:
        return AppColors.surfaceBlue.withValues(alpha: 0.05);
      case CardVariant.outlined:
      case CardVariant.filled:
        return null;
    }
  }

  BoxDecoration? _getDecoration() {
    switch (variant) {
      case CardVariant.outlined:
        return BoxDecoration(
          border: Border.all(
            color: borderColor ?? AppColors.textHint.withValues(alpha: 0.3),
            width: 1,
          ),
          borderRadius: borderRadius ?? BorderRadius.circular(16),
        );
      case CardVariant.elevated:
      case CardVariant.filled:
        return null;
    }
  }
}

class InfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const InfoCard({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ModernCard(
      onTap: onTap,
      padding: padding ?? const EdgeInsets.all(16),
      margin: margin,
      child: Row(
        children: [
          if (leading != null) ...[leading!, const SizedBox(width: 16)],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[const SizedBox(width: 16), trailing!],
        ],
      ),
    );
  }
}

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool showTrend;
  final String? trendValue;
  final bool? trendIsPositive;
  final String? subtitle;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.onTap,
    this.padding,
    this.margin,
    this.showTrend = false,
    this.trendValue,
    this.trendIsPositive,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = color ?? AppColors.primaryBlue;

    return ModernCard(
      onTap: onTap,
      padding: padding ?? const EdgeInsets.all(12),
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: cardColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: cardColor, size: 18),
              ),
              const Spacer(),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: AppColors.textHint,
                ),
            ],
          ),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: cardColor,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
