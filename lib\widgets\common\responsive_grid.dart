import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../utils/responsive_utils.dart';

/// Responsive grid layout that adapts to different screen sizes
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? maxColumns;
  final double? spacing;
  final double? runSpacing;
  final double? childAspectRatio;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.maxColumns,
    this.spacing,
    this.runSpacing,
    this.childAspectRatio,
    this.padding,
    this.shrinkWrap = true,
    this.physics = const NeverScrollableScrollPhysics(),
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final columns = ResponsiveUtils.getGridColumns(context, maxColumns: maxColumns);
        final effectiveSpacing = spacing ?? ResponsiveUtils.getResponsiveSpacing(context, AppConstants.gridSpacing);
        final effectiveRunSpacing = runSpacing ?? effectiveSpacing;
        final effectiveAspectRatio = childAspectRatio ?? ResponsiveUtils.getCardAspectRatio(context);

        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: GridView.builder(
            shrinkWrap: shrinkWrap,
            physics: physics,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              crossAxisSpacing: effectiveSpacing,
              mainAxisSpacing: effectiveRunSpacing,
              childAspectRatio: effectiveAspectRatio,
            ),
            itemCount: children.length,
            itemBuilder: (context, index) => children[index],
          ),
        );
      },
    );
  }
}

/// Responsive staggered grid for cards with different heights
class ResponsiveStaggeredGrid extends StatelessWidget {
  final List<Widget> children;
  final int? maxColumns;
  final double? spacing;
  final EdgeInsetsGeometry? padding;

  const ResponsiveStaggeredGrid({
    super.key,
    required this.children,
    this.maxColumns,
    this.spacing,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final columns = ResponsiveUtils.getGridColumns(context, maxColumns: maxColumns);
        final effectiveSpacing = spacing ?? ResponsiveUtils.getResponsiveSpacing(context, AppConstants.gridSpacing);

        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: _buildStaggeredGrid(columns, effectiveSpacing),
        );
      },
    );
  }

  Widget _buildStaggeredGrid(int columns, double spacing) {
    if (columns == 1) {
      return Column(
        children: children.map((child) => Padding(
          padding: EdgeInsets.only(bottom: spacing),
          child: child,
        )).toList(),
      );
    }

    // Create column lists
    final columnChildren = List.generate(columns, (index) => <Widget>[]);
    
    // Distribute children across columns
    for (int i = 0; i < children.length; i++) {
      final columnIndex = i % columns;
      columnChildren[columnIndex].add(children[i]);
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columnChildren.asMap().entries.map((entry) {
        final columnIndex = entry.key;
        final columnWidgets = entry.value;
        
        return Expanded(
          child: Padding(
            padding: EdgeInsets.only(
              left: columnIndex > 0 ? spacing / 2 : 0,
              right: columnIndex < columns - 1 ? spacing / 2 : 0,
            ),
            child: Column(
              children: columnWidgets.map((widget) => Padding(
                padding: EdgeInsets.only(bottom: spacing),
                child: widget,
              )).toList(),
            ),
          ),
        );
      }).toList(),
    );
  }
}

/// Responsive wrap layout for flexible item arrangement
class ResponsiveWrap extends StatelessWidget {
  final List<Widget> children;
  final double? spacing;
  final double? runSpacing;
  final WrapAlignment alignment;
  final WrapCrossAlignment crossAxisAlignment;
  final EdgeInsetsGeometry? padding;

  const ResponsiveWrap({
    super.key,
    required this.children,
    this.spacing,
    this.runSpacing,
    this.alignment = WrapAlignment.start,
    this.crossAxisAlignment = WrapCrossAlignment.start,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveSpacing = spacing ?? ResponsiveUtils.getResponsiveSpacing(context, AppConstants.itemSpacing);
    final effectiveRunSpacing = runSpacing ?? effectiveSpacing;

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Wrap(
        spacing: effectiveSpacing,
        runSpacing: effectiveRunSpacing,
        alignment: alignment,
        crossAxisAlignment: crossAxisAlignment,
        children: children,
      ),
    );
  }
}

/// Responsive list view with adaptive item heights
class ResponsiveListView extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final double? spacing;

  const ResponsiveListView({
    super.key,
    required this.children,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.spacing,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveSpacing = spacing ?? ResponsiveUtils.getResponsiveSpacing(context, AppConstants.itemSpacing);
    final responsivePadding = padding ?? ResponsiveUtils.getResponsivePadding(context);

    return ListView.separated(
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: responsivePadding,
      itemCount: children.length,
      separatorBuilder: (context, index) => SizedBox(height: effectiveSpacing),
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// Responsive card container with adaptive styling
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;
  final VoidCallback? onTap;
  final bool showBorder;
  final List<Color>? gradientColors;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.onTap,
    this.showBorder = false,
    this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    final borderRadius = ResponsiveUtils.getBorderRadius(context, AppConstants.borderRadius);
    final cardElevation = elevation ?? ResponsiveUtils.getCardElevation(context);
    final responsivePadding = padding ?? ResponsiveUtils.getResponsivePadding(context);

    return Container(
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: gradientColors != null
            ? LinearGradient(
                colors: gradientColors!,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: gradientColors == null ? (color ?? Colors.white) : null,
        border: showBorder
            ? Border.all(
                color: Theme.of(context).dividerColor,
                width: 1,
              )
            : null,
        boxShadow: cardElevation > 0
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: cardElevation * 2,
                  offset: Offset(0, cardElevation / 2),
                  spreadRadius: 0,
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: responsivePadding,
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Responsive section header with adaptive styling
class ResponsiveSectionHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? action;
  final EdgeInsetsGeometry? padding;

  const ResponsiveSectionHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.action,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final titleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 20);
    final subtitleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 14);
    final responsivePadding = padding ?? ResponsiveUtils.getResponsivePadding(context);

    return Padding(
      padding: responsivePadding,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontSize: subtitleFontSize,
                      color: theme.textTheme.bodySmall?.color,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          if (action != null) ...[
            const SizedBox(width: 16),
            action!,
          ],
        ],
      ),
    );
  }
}
