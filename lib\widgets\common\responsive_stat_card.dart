import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../utils/responsive_utils.dart';

/// Modern responsive statistics card with professional styling
class ResponsiveStatCard extends StatefulWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;
  final String? subtitle;
  final String? trendValue;
  final bool? trendIsPositive;
  final bool showTrend;
  final bool isLoading;
  final Widget? customIcon;
  final List<Color>? gradientColors;

  const ResponsiveStatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.onTap,
    this.subtitle,
    this.trendValue,
    this.trendIsPositive,
    this.showTrend = false,
    this.isLoading = false,
    this.customIcon,
    this.gradientColors,
  });

  @override
  State<ResponsiveStatCard> createState() => _ResponsiveStatCardState();
}

class _ResponsiveStatCardState extends State<ResponsiveStatCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = widget.color ?? AppColors.primaryBlue;
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildCard(context, theme, cardColor, deviceType),
          ),
        );
      },
    );
  }

  Widget _buildCard(BuildContext context, ThemeData theme, Color cardColor, DeviceType deviceType) {
    final borderRadius = ResponsiveUtils.getBorderRadius(context, AppConstants.borderRadius);
    final elevation = ResponsiveUtils.getCardElevation(context);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: widget.gradientColors != null
            ? LinearGradient(
                colors: widget.gradientColors!,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: widget.gradientColors == null ? AppColors.cardBackground : null,
        boxShadow: [
          BoxShadow(
            color: cardColor.withValues(alpha: 0.1),
            blurRadius: elevation * 2,
            offset: Offset(0, elevation / 2),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: elevation,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          onTap: widget.onTap,
          onTapDown: widget.onTap != null ? (_) => _animationController.forward() : null,
          onTapUp: widget.onTap != null ? (_) => _animationController.reverse() : null,
          onTapCancel: widget.onTap != null ? () => _animationController.reverse() : null,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: _getResponsivePadding(deviceType),
            child: widget.isLoading ? _buildLoadingState() : _buildContent(context, theme, cardColor, deviceType),
          ),
        ),
      ),
    );
  }

  EdgeInsets _getResponsivePadding(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16);
      case DeviceType.tablet:
        return const EdgeInsets.all(20);
      case DeviceType.desktop:
        return const EdgeInsets.all(24);
    }
  }

  Widget _buildLoadingState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: AppColors.borderLight,
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            const Spacer(),
            Container(
              width: 40,
              height: 16,
              decoration: BoxDecoration(
                color: AppColors.borderLight,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          height: 24,
          decoration: BoxDecoration(
            color: AppColors.borderLight,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 80,
          height: 16,
          decoration: BoxDecoration(
            color: AppColors.borderLight,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme, Color cardColor, DeviceType deviceType) {
    final iconSize = ResponsiveUtils.getResponsiveIconSize(context, 24);
    final titleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 14);
    final valueFontSize = ResponsiveUtils.getResponsiveFontSize(context, 24);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with icon and trend
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cardColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: widget.customIcon ?? Icon(
                widget.icon,
                color: cardColor,
                size: iconSize,
              ),
            ),
            const Spacer(),
            if (widget.showTrend && widget.trendValue != null) _buildTrendIndicator(cardColor),
          ],
        ),
        
        SizedBox(height: deviceType == DeviceType.mobile ? 12 : 16),
        
        // Value
        FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            widget.value,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontSize: valueFontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
              letterSpacing: -0.5,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        
        const SizedBox(height: 4),
        
        // Title
        Text(
          widget.title,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontSize: titleFontSize,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        // Subtitle if provided
        if (widget.subtitle != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.subtitle!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.textHint,
              fontSize: ResponsiveUtils.getResponsiveFontSize(context, 12),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildTrendIndicator(Color cardColor) {
    if (widget.trendValue == null) return const SizedBox.shrink();
    
    final isPositive = widget.trendIsPositive ?? true;
    final trendColor = isPositive ? AppColors.success : AppColors.error;
    final trendIcon = isPositive ? Icons.trending_up : Icons.trending_down;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: trendColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: trendColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            trendIcon,
            color: trendColor,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            widget.trendValue!,
            style: TextStyle(
              color: trendColor,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
