import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../utils/responsive_utils.dart';
import '../../models/user.dart';

/// Modern welcome card with gradient background and professional styling
class ModernWelcomeCard extends StatelessWidget {
  final User? user;
  final String? customGreeting;
  final Widget? action;
  final bool showTime;
  final List<Color>? gradientColors;

  const ModernWelcomeCard({
    super.key,
    this.user,
    this.customGreeting,
    this.action,
    this.showTime = true,
    this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final borderRadius = ResponsiveUtils.getBorderRadius(context, AppConstants.largeBorderRadius);
    
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: gradientColors != null
            ? LinearGradient(
                colors: gradientColors!,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : const LinearGradient(
                colors: [
                  AppColors.primaryBlue,
                  AppColors.primaryBlueDark,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: Padding(
          padding: _getResponsivePadding(deviceType),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context, theme, deviceType),
              if (showTime) ...[
                SizedBox(height: deviceType == DeviceType.mobile ? 8 : 12),
                _buildTimeInfo(context, theme, deviceType),
              ],
              if (action != null) ...[
                SizedBox(height: deviceType == DeviceType.mobile ? 16 : 20),
                action!,
              ],
            ],
          ),
        ),
      ),
    );
  }

  EdgeInsets _getResponsivePadding(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(20);
      case DeviceType.tablet:
        return const EdgeInsets.all(24);
      case DeviceType.desktop:
        return const EdgeInsets.all(28);
    }
  }

  Widget _buildHeader(BuildContext context, ThemeData theme, DeviceType deviceType) {
    final titleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 24);
    final subtitleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 16);
    
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  _getGreeting(),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textWhite,
                    letterSpacing: -0.5,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (user != null) ...[
                const SizedBox(height: 4),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    user!.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontSize: subtitleFontSize,
                      color: AppColors.textWhite.withValues(alpha: 0.9),
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ),
        _buildAvatar(deviceType),
      ],
    );
  }

  Widget _buildAvatar(DeviceType deviceType) {
    final avatarSize = deviceType == DeviceType.mobile ? 50.0 : 60.0;
    
    return Container(
      width: avatarSize,
      height: avatarSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.textWhite.withValues(alpha: 0.2),
        border: Border.all(
          color: AppColors.textWhite.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: user?.profileImageUrl != null
          ? ClipOval(
              child: Image.network(
                user!.profileImageUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(avatarSize),
              ),
            )
          : _buildDefaultAvatar(avatarSize),
    );
  }

  Widget _buildDefaultAvatar(double size) {
    return Icon(
      Icons.person,
      color: AppColors.textWhite.withValues(alpha: 0.8),
      size: size * 0.6,
    );
  }

  Widget _buildTimeInfo(BuildContext context, ThemeData theme, DeviceType deviceType) {
    final now = DateTime.now();
    final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    final dateString = _getFormattedDate(now);
    final timeFontSize = ResponsiveUtils.getResponsiveFontSize(context, 14);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.textWhite.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.textWhite.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time,
            color: AppColors.textWhite.withValues(alpha: 0.9),
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            timeString,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontSize: timeFontSize,
              color: AppColors.textWhite,
              fontWeight: FontWeight.w600,
              fontFeatures: [const FontFeature.tabularFigures()],
            ),
          ),
          const SizedBox(width: 12),
          Container(
            width: 1,
            height: 16,
            color: AppColors.textWhite.withValues(alpha: 0.3),
          ),
          const SizedBox(width: 12),
          Flexible(
            child: Text(
              dateString,
              style: theme.textTheme.bodySmall?.copyWith(
                fontSize: timeFontSize - 2,
                color: AppColors.textWhite.withValues(alpha: 0.8),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  String _getGreeting() {
    if (customGreeting != null) return customGreeting!;
    
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'صباح الخير';
    } else if (hour < 17) {
      return 'مساء الخير';
    } else {
      return 'مساء الخير';
    }
  }

  String _getFormattedDate(DateTime date) {
    const arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    const arabicDays = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
    ];
    
    final dayName = arabicDays[date.weekday - 1];
    final monthName = arabicMonths[date.month - 1];
    
    return '$dayName، ${date.day} $monthName ${date.year}';
  }
}

/// Compact welcome card for smaller spaces
class CompactWelcomeCard extends StatelessWidget {
  final User? user;
  final String? greeting;
  final VoidCallback? onTap;

  const CompactWelcomeCard({
    super.key,
    this.user,
    this.greeting,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final borderRadius = ResponsiveUtils.getBorderRadius(context, AppConstants.borderRadius);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: const LinearGradient(
          colors: [
            AppColors.surfaceBlue,
            AppColors.primaryBlueLight,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primaryBlue.withValues(alpha: 0.2),
                  ),
                  child: Icon(
                    Icons.person,
                    color: AppColors.primaryBlue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        greeting ?? 'مرحباً',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (user != null)
                        Text(
                          user!.name,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.primaryBlue,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
