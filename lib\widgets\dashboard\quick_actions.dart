import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/attendance_provider.dart';
import '../../routes/app_routes.dart';
import '../../utils/responsive_utils.dart';

class QuickActions extends StatelessWidget {
  final AttendanceProvider attendanceProvider;

  const QuickActions({super.key, required this.attendanceProvider});

  @override
  Widget build(BuildContext context) {
    final borderRadius = ResponsiveUtils.getBorderRadius(
      context,
      AppConstants.largeBorderRadius,
    );
    final elevation = ResponsiveUtils.getCardElevation(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        color: AppColors.backgroundWhite,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: elevation * 2,
            offset: Offset(0, elevation / 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: ResponsiveUtils.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            SizedBox(height: ResponsiveUtils.isMobile(context) ? 16 : 20),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final titleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 20);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.success.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.flash_on,
            color: AppColors.success,
            size: ResponsiveUtils.getResponsiveIconSize(context, 20),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'إجراءات سريعة',
            style: theme.textTheme.titleLarge?.copyWith(
              fontSize: titleFontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);

    if (deviceType == DeviceType.mobile) {
      return Column(
        children: [
          _buildActionButton(
            context,
            title: 'تسجيل الحضور',
            subtitle: 'سجل حضورك أو انصرافك',
            icon: Icons.access_time,
            color: AppColors.success,
            onTap: () =>
                Navigator.pushNamed(context, AppRoutes.attendanceScreen),
          ),
          const SizedBox(height: 12),
          _buildActionButton(
            context,
            title: 'الملف الشخصي',
            subtitle: 'عرض وتعديل بياناتك',
            icon: Icons.person,
            color: AppColors.primaryBlue,
            onTap: () => Navigator.pushNamed(context, AppRoutes.profileScreen),
          ),
          const SizedBox(height: 12),
          _buildActionButton(
            context,
            title: 'سجل الحضور',
            subtitle: 'عرض تاريخ الحضور',
            icon: Icons.history,
            color: AppColors.info,
            onTap: () =>
                Navigator.pushNamed(context, AppRoutes.attendanceScreen),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
            child: _buildActionButton(
              context,
              title: 'تسجيل الحضور',
              subtitle: 'سجل حضورك أو انصرافك',
              icon: Icons.access_time,
              color: AppColors.success,
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.attendanceScreen),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              context,
              title: 'الملف الشخصي',
              subtitle: 'عرض وتعديل بياناتك',
              icon: Icons.person,
              color: AppColors.primaryBlue,
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.profileScreen),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              context,
              title: 'سجل الحضور',
              subtitle: 'عرض تاريخ الحضور',
              icon: Icons.history,
              color: AppColors.info,
              onTap: () =>
                  Navigator.pushNamed(context, AppRoutes.attendanceScreen),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildActionButton(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final borderRadius = ResponsiveUtils.getBorderRadius(
      context,
      AppConstants.borderRadius,
    );
    final buttonHeight = ResponsiveUtils.getButtonHeight(context);
    final titleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 14);
    final subtitleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 12);

    return Container(
      height: ResponsiveUtils.isMobile(context)
          ? buttonHeight + 20
          : buttonHeight + 30,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: ResponsiveUtils.getResponsiveIconSize(context, 20),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          title,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontSize: titleFontSize,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontSize: subtitleFontSize,
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Icon(Icons.arrow_forward_ios, color: color, size: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
