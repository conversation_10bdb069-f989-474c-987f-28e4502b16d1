import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/attendance_provider.dart';
import '../../utils/responsive_utils.dart';

class StatusCard extends StatelessWidget {
  final AttendanceProvider attendanceProvider;

  const StatusCard({super.key, required this.attendanceProvider});

  @override
  Widget build(BuildContext context) {
    final borderRadius = ResponsiveUtils.getBorderRadius(
      context,
      AppConstants.largeBorderRadius,
    );
    final elevation = ResponsiveUtils.getCardElevation(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: const LinearGradient(
          colors: [AppColors.backgroundWhite, AppColors.surfaceGrey],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: elevation * 2,
            offset: Offset(0, elevation / 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: ResponsiveUtils.getResponsivePadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            SizedBox(height: ResponsiveUtils.isMobile(context) ? 16 : 20),
            _buildStatusGrid(context),
            if (attendanceProvider.assignedSite != null) ...[
              SizedBox(height: ResponsiveUtils.isMobile(context) ? 16 : 20),
              _buildLocationInfo(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final titleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 20);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.dashboard,
            color: AppColors.primaryBlue,
            size: ResponsiveUtils.getResponsiveIconSize(context, 20),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'الحالة الحالية',
            style: theme.textTheme.titleLarge?.copyWith(
              fontSize: titleFontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusGrid(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatusItem(
            context,
            icon: Icons.access_time,
            title: 'الحالة',
            value: attendanceProvider.getStatusText(),
            color: attendanceProvider.hasActivePointage
                ? AppColors.success
                : AppColors.textSecondary,
          ),
        ),
        SizedBox(width: ResponsiveUtils.isMobile(context) ? 12 : 16),
        Expanded(
          child: _buildStatusItem(
            context,
            icon: Icons.schedule,
            title: 'ساعات اليوم',
            value: attendanceProvider.getWorkingHoursToday(),
            color: AppColors.primaryBlue,
          ),
        ),
      ],
    );
  }

  Widget _buildLocationInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: _buildStatusItem(
        context,
        icon: Icons.location_on,
        title: 'الموقع المخصص',
        value: attendanceProvider.assignedSite!.name,
        color: AppColors.info,
      ),
    );
  }

  Widget _buildStatusItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final iconSize = ResponsiveUtils.getResponsiveIconSize(context, 20);
    final titleFontSize = ResponsiveUtils.getResponsiveFontSize(context, 12);
    final valueFontSize = ResponsiveUtils.getResponsiveFontSize(context, 16);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(icon, color: color, size: iconSize),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: titleFontSize,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontSize: valueFontSize,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
