import 'package:flutter/material.dart';
import 'package:clockin/services/http_service.dart';
import 'package:clockin/constants/app_constants.dart';

/// Widget de test pour valider la correction du HttpService en temps réel
/// À ajouter temporairement pour confirmer que response.data n'est plus null
class TestHttpFixWidget extends StatefulWidget {
  const TestHttpFixWidget({super.key});

  @override
  State<TestHttpFixWidget> createState() => _TestHttpFixWidgetState();
}

class _TestHttpFixWidgetState extends State<TestHttpFixWidget> {
  final HttpService _httpService = HttpService();
  String _testResult = 'Prêt à tester la correction HTTP';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _httpService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Correction HTTP'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Info sur le test
            Card(
              color: Colors.green.withValues(alpha: 0.1),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test de Correction HttpService',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('URL: ${AppConstants.baseUrl}'),
                    Text('Endpoint: ${AppConstants.pointageEndpoint}'),
                    const SizedBox(height: 8),
                    const Text(
                      'Ce test va appeler directement l\'endpoint /api/pointage pour vérifier que response.data n\'est plus null.',
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Boutons de test
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testPointageEndpoint,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('🔧 Test Endpoint Pointage'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testWithSpecificUser,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('👤 Test Utilisateur 5'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Résultats
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Résultats du Test HTTP',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: SingleChildScrollView(
                            child: Text(
                              _testResult,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testPointageEndpoint() async {
    setState(() {
      _isLoading = true;
      _testResult = '🚀 Test de l\'endpoint pointage...\n';
    });

    try {
      _appendLog('📡 Appel de l\'endpoint: ${AppConstants.pointageEndpoint}');
      _appendLog('🔧 Test de la correction HttpService');
      _appendLog('');

      // Appel direct sans fromJson pour tester la correction
      final response = await _httpService.get(
        AppConstants.pointageEndpoint,
        queryParameters: {'start_date': '2025-07-29', 'end_date': '2025-08-05'},
      );

      _appendLog('📊 Réponse reçue:');
      _appendLog('   success: ${response.success}');
      _appendLog('   message: ${response.message}');
      _appendLog('   data type: ${response.data.runtimeType}');
      _appendLog('   data is null: ${response.data == null}');

      if (response.data != null) {
        _appendLog('   ✅ CORRECTION RÉUSSIE: response.data n\'est plus null !');

        if (response.data is List) {
          final dataList = response.data as List;
          _appendLog(
            '   📋 Données de type List, longueur: ${dataList.length}',
          );

          if (dataList.isNotEmpty) {
            _appendLog('   📄 Premier élément: ${dataList.first}');
            _appendLog('');
            _appendLog('🎉 SUCCÈS COMPLET:');
            _appendLog('   ✅ L\'endpoint répond correctement');
            _appendLog('   ✅ response.data contient les vraies données');
            _appendLog(
              '   ✅ Le fallback des rapports devrait maintenant fonctionner',
            );
          } else {
            _appendLog('   ⚠️ Liste vide - pas de données pour cette période');
          }
        } else if (response.data is Map) {
          final dataMap = response.data as Map;
          _appendLog(
            '   📋 Données de type Map, clés: ${dataMap.keys.toList()}',
          );
        } else {
          _appendLog(
            '   📋 Type de données inattendu: ${response.data.runtimeType}',
          );
        }
      } else {
        _appendLog('   ❌ PROBLÈME PERSISTANT: response.data est encore null');
        _appendLog('   🔍 Vérifiez les logs HttpService pour plus de détails');
      }
    } catch (e) {
      _appendLog('');
      _appendLog('💥 Erreur capturée:');
      _appendLog('   Type: ${e.runtimeType}');
      _appendLog('   Message: $e');

      if (e.toString().contains('404')) {
        _appendLog('');
        _appendLog('💡 Erreur 404 - Endpoint non trouvé');
        _appendLog(
          '   Ceci est normal si l\'endpoint n\'existe pas sur ce serveur',
        );
        _appendLog(
          '   Mais nous pouvons voir si la correction HTTP fonctionne',
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testWithSpecificUser() async {
    setState(() {
      _isLoading = true;
      _testResult = '🚀 Test avec utilisateur spécifique...\n';
    });

    try {
      _appendLog('👤 Test pour l\'utilisateur ID 5 (سماح زهراوي)');
      _appendLog('📡 Endpoint: ${AppConstants.pointageEndpoint}');
      _appendLog('');

      final response = await _httpService.get(
        AppConstants.pointageEndpoint,
        queryParameters: {
          'user_id': '5',
          'start_date': '2025-07-29',
          'end_date': '2025-08-05',
        },
      );

      _appendLog('📊 Réponse pour utilisateur 5:');
      _appendLog('   success: ${response.success}');
      _appendLog('   data type: ${response.data.runtimeType}');
      _appendLog('   data is null: ${response.data == null}');

      if (response.success && response.data != null) {
        _appendLog('   ✅ Données récupérées avec succès !');

        if (response.data is List) {
          final dataList = response.data as List;
          _appendLog('   📋 Nombre d\'enregistrements: ${dataList.length}');

          for (int i = 0; i < dataList.length && i < 2; i++) {
            final item = dataList[i];
            if (item is Map) {
              _appendLog('   📄 Enregistrement ${i + 1}:');
              _appendLog('      ID: ${item['id']}');
              _appendLog('      Utilisateur: ${item['user']?['name']}');
              _appendLog('      Site: ${item['site']?['name']}');
              _appendLog('      Début: ${item['debut_pointage']}');
              _appendLog('      Fin: ${item['fin_pointage']}');
            }
          }

          _appendLog('');
          _appendLog('🎯 VALIDATION COMPLÈTE:');
          _appendLog('   ✅ HttpService corrigé fonctionne');
          _appendLog('   ✅ Données utilisateur récupérées');
          _appendLog(
            '   ✅ Le fallback des rapports est maintenant opérationnel',
          );
        }
      } else {
        _appendLog('   ⚠️ Pas de données ou échec de la requête');
      }
    } catch (e) {
      _appendLog('');
      _appendLog('💥 Erreur: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _appendLog(String message) {
    setState(() {
      _testResult += '$message\n';
    });

    // Aussi dans les logs Flutter
    print('TestHttpFix: $message');
  }
}

/// Instructions d'intégration:
/// 
/// 1. Ajoutez ce fichier dans lib/widgets/debug/
/// 
/// 2. Dans votre écran admin, ajoutez:
/// 
/// FloatingActionButton(
///   onPressed: () {
///     Navigator.push(
///       context,
///       MaterialPageRoute(builder: (context) => const TestHttpFixWidget()),
///     );
///   },
///   backgroundColor: Colors.green,
///   child: const Icon(Icons.http),
/// )
